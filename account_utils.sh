#!/bin/bash

# Account Utilities Script
# Additional utilities for account management

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
JSON_FILE="${SCRIPT_DIR}/account.json"
CSV_FILE="${SCRIPT_DIR}/accounts.csv"
LOG_FILE="${SCRIPT_DIR}/account_utils.log"
BACKUP_DIR="${SCRIPT_DIR}/backups"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
    
    case "$level" in
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message" >&2
            ;;
        "SUCCESS")
            echo -e "${GREEN}[SUCCESS]${NC} $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[WARNING]${NC} $message"
            ;;
        "INFO")
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
        *)
            echo "$message"
            ;;
    esac
}

# Create backup directory
create_backup_dir() {
    if [ ! -d "$BACKUP_DIR" ]; then
        mkdir -p "$BACKUP_DIR"
        log "INFO" "Created backup directory: $BACKUP_DIR"
    fi
}

# Backup files
backup_files() {
    create_backup_dir
    
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local backup_json="${BACKUP_DIR}/account_${timestamp}.json"
    local backup_csv="${BACKUP_DIR}/accounts_${timestamp}.csv"
    
    if [ -f "$JSON_FILE" ]; then
        cp "$JSON_FILE" "$backup_json"
        log "SUCCESS" "Backed up JSON file to: $backup_json"
    fi
    
    if [ -f "$CSV_FILE" ]; then
        cp "$CSV_FILE" "$backup_csv"
        log "SUCCESS" "Backed up CSV file to: $backup_csv"
    fi
}

# Restore from backup
restore_backup() {
    local backup_file="$1"
    
    if [ ! -f "$backup_file" ]; then
        log "ERROR" "Backup file not found: $backup_file"
        return 1
    fi
    
    local extension="${backup_file##*.}"
    
    case "$extension" in
        "json")
            cp "$backup_file" "$JSON_FILE"
            log "SUCCESS" "Restored JSON file from: $backup_file"
            ;;
        "csv")
            cp "$backup_file" "$CSV_FILE"
            log "SUCCESS" "Restored CSV file from: $backup_file"
            ;;
        *)
            log "ERROR" "Unknown backup file type: $extension"
            return 1
            ;;
    esac
}

# List backups
list_backups() {
    create_backup_dir
    
    log "INFO" "Available backups:"
    echo
    
    if [ -n "$(ls -A "$BACKUP_DIR" 2>/dev/null)" ]; then
        ls -la "$BACKUP_DIR"
    else
        log "INFO" "No backups found"
    fi
}

# Validate JSON structure
validate_json() {
    if [ ! -f "$JSON_FILE" ]; then
        log "ERROR" "JSON file not found: $JSON_FILE"
        return 1
    fi
    
    if ! jq empty "$JSON_FILE" 2>/dev/null; then
        log "ERROR" "Invalid JSON format in: $JSON_FILE"
        return 1
    fi
    
    # Check if it's an array
    if ! jq -e 'type == "array"' "$JSON_FILE" >/dev/null; then
        log "ERROR" "JSON file should contain an array of accounts"
        return 1
    fi
    
    # Validate each account has required fields
    local missing_fields=()
    local account_count=$(jq length "$JSON_FILE")
    
    for ((i=0; i<account_count; i++)); do
        local account=$(jq ".[$i]" "$JSON_FILE")
        
        if ! echo "$account" | jq -e 'has("email")' >/dev/null; then
            missing_fields+=("email in account $((i+1))")
        fi
        
        if ! echo "$account" | jq -e 'has("password")' >/dev/null; then
            missing_fields+=("password in account $((i+1))")
        fi
        
        if ! echo "$account" | jq -e 'has("reward")' >/dev/null; then
            missing_fields+=("reward in account $((i+1))")
        fi
        
        if ! echo "$account" | jq -e 'has("status")' >/dev/null; then
            missing_fields+=("status in account $((i+1))")
        fi
    done
    
    if [ ${#missing_fields[@]} -ne 0 ]; then
        log "ERROR" "Missing required fields:"
        for field in "${missing_fields[@]}"; do
            log "ERROR" "  - $field"
        done
        return 1
    fi
    
    log "SUCCESS" "JSON file validation passed ($account_count accounts)"
}

# Add new account
add_account() {
    local email="$1"
    local password="$2"
    local reward="${3:-0}"
    local status="${4:-available}"
    
    # Check if account already exists
    if jq -e --arg email "$email" '.[] | select(.email == $email)' "$JSON_FILE" >/dev/null; then
        log "ERROR" "Account already exists: $email"
        return 1
    fi
    
    # Add new account to JSON
    jq --arg email "$email" --arg password "$password" --argjson reward "$reward" --arg status "$status" \
       '. += [{"email": $email, "password": $password, "reward": $reward, "status": $status}]' \
       "$JSON_FILE" > "${JSON_FILE}.tmp" && mv "${JSON_FILE}.tmp" "$JSON_FILE"
    
    log "SUCCESS" "Added new account: $email"
}

# Remove account
remove_account() {
    local email="$1"
    
    # Check if account exists
    if ! jq -e --arg email "$email" '.[] | select(.email == $email)' "$JSON_FILE" >/dev/null; then
        log "ERROR" "Account not found: $email"
        return 1
    fi
    
    # Remove account from JSON
    jq --arg email "$email" 'map(select(.email != $email))' \
       "$JSON_FILE" > "${JSON_FILE}.tmp" && mv "${JSON_FILE}.tmp" "$JSON_FILE"
    
    log "SUCCESS" "Removed account: $email"
}

# Reset all rewards
reset_rewards() {
    log "INFO" "Resetting all account rewards to 0..."
    
    jq 'map(.reward = 0)' "$JSON_FILE" > "${JSON_FILE}.tmp" && mv "${JSON_FILE}.tmp" "$JSON_FILE"
    
    log "SUCCESS" "Reset all account rewards to 0"
}

# Reset all statuses
reset_statuses() {
    local new_status="${1:-available}"
    
    log "INFO" "Resetting all account statuses to $new_status..."
    
    jq --arg status "$new_status" 'map(.status = $status)' \
       "$JSON_FILE" > "${JSON_FILE}.tmp" && mv "${JSON_FILE}.tmp" "$JSON_FILE"
    
    log "SUCCESS" "Reset all account statuses to $new_status"
}

# Generate statistics
generate_stats() {
    log "INFO" "Generating account statistics..."
    echo
    
    local total_accounts=$(jq length "$JSON_FILE")
    local available_accounts=$(jq '[.[] | select(.status == "available")] | length' "$JSON_FILE")
    local processing_accounts=$(jq '[.[] | select(.status == "processing")] | length' "$JSON_FILE")
    local total_rewards=$(jq '[.[].reward] | add' "$JSON_FILE")
    local avg_reward=$(jq '[.[].reward] | add / length' "$JSON_FILE")
    
    echo "=== Account Statistics ==="
    echo "Total Accounts: $total_accounts"
    echo "Available: $available_accounts"
    echo "Processing: $processing_accounts"
    echo "Other Status: $((total_accounts - available_accounts - processing_accounts))"
    echo "Total Rewards: $total_rewards"
    echo "Average Reward: $avg_reward"
    echo
    
    # Status breakdown
    echo "=== Status Breakdown ==="
    jq -r 'group_by(.status) | .[] | "\(.[0].status): \(length)"' "$JSON_FILE"
    echo
}

# Export to different formats
export_data() {
    local format="$1"
    local output_file="$2"
    
    case "$format" in
        "csv")
            echo "email,password,reward,status" > "$output_file"
            jq -r '.[] | [.email, .password, .reward, .status] | @csv' "$JSON_FILE" >> "$output_file"
            log "SUCCESS" "Exported data to CSV: $output_file"
            ;;
        "tsv")
            echo -e "email\tpassword\treward\tstatus" > "$output_file"
            jq -r '.[] | [.email, .password, .reward, .status] | @tsv' "$JSON_FILE" >> "$output_file"
            log "SUCCESS" "Exported data to TSV: $output_file"
            ;;
        "json")
            cp "$JSON_FILE" "$output_file"
            log "SUCCESS" "Exported data to JSON: $output_file"
            ;;
        *)
            log "ERROR" "Unsupported export format: $format"
            return 1
            ;;
    esac
}

# Show usage information
show_usage() {
    cat << EOF
Account Utilities Script

Usage: $0 [COMMAND] [OPTIONS]

Commands:
    backup                  Create backup of current files
    restore FILE            Restore from backup file
    list-backups           List available backups
    validate               Validate JSON file structure
    add EMAIL PASSWORD [REWARD] [STATUS]    Add new account
    remove EMAIL           Remove account
    reset-rewards          Reset all rewards to 0
    reset-statuses [STATUS]    Reset all statuses (default: available)
    stats                  Generate account statistics
    export FORMAT FILE     Export data (formats: csv, tsv, json)

Examples:
    $0 backup
    $0 validate
    $0 add <EMAIL> password123 0 available
    $0 remove <EMAIL>
    $0 reset-rewards
    $0 stats
    $0 export csv accounts_export.csv

EOF
}

# Main function
main() {
    case "${1:-}" in
        "backup")
            backup_files
            ;;
        "restore")
            if [ $# -ne 2 ]; then
                log "ERROR" "Usage: $0 restore BACKUP_FILE"
                exit 1
            fi
            restore_backup "$2"
            ;;
        "list-backups")
            list_backups
            ;;
        "validate")
            validate_json
            ;;
        "add")
            if [ $# -lt 3 ]; then
                log "ERROR" "Usage: $0 add EMAIL PASSWORD [REWARD] [STATUS]"
                exit 1
            fi
            add_account "$2" "$3" "${4:-0}" "${5:-available}"
            ;;
        "remove")
            if [ $# -ne 2 ]; then
                log "ERROR" "Usage: $0 remove EMAIL"
                exit 1
            fi
            remove_account "$2"
            ;;
        "reset-rewards")
            reset_rewards
            ;;
        "reset-statuses")
            reset_statuses "${2:-available}"
            ;;
        "stats")
            generate_stats
            ;;
        "export")
            if [ $# -ne 3 ]; then
                log "ERROR" "Usage: $0 export FORMAT OUTPUT_FILE"
                exit 1
            fi
            export_data "$2" "$3"
            ;;
        "help"|"-h"|"--help"|"")
            show_usage
            ;;
        *)
            log "ERROR" "Unknown command: $1"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
