#!/bin/bash

# Account Manager Script
# Manages multiple accounts with JSON and CSV file integration

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
JSON_FILE="${SCRIPT_DIR}/account.json"
CSV_FILE="${SCRIPT_DIR}/accounts.csv"
LOG_FILE="${SCRIPT_DIR}/account_manager.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
    
    case "$level" in
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message" >&2
            ;;
        "SUCCESS")
            echo -e "${GREEN}[SUCCESS]${NC} $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[WARNING]${NC} $message"
            ;;
        "INFO")
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
        *)
            echo "$message"
            ;;
    esac
}

# Check if required tools are installed
check_dependencies() {
    local missing_deps=()
    
    if ! command -v jq &> /dev/null; then
        missing_deps+=("jq")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log "ERROR" "Missing dependencies: ${missing_deps[*]}"
        log "INFO" "Please install missing dependencies:"
        for dep in "${missing_deps[@]}"; do
            case "$dep" in
                "jq")
                    log "INFO" "  Ubuntu/Debian: sudo apt-get install jq"
                    log "INFO" "  CentOS/RHEL: sudo yum install jq"
                    log "INFO" "  macOS: brew install jq"
                    ;;
            esac
        done
        exit 1
    fi
}

# Initialize CSV file with headers
init_csv() {
    if [ ! -f "$CSV_FILE" ]; then
        echo "email,password,reward,status" > "$CSV_FILE"
        log "INFO" "Created CSV file: $CSV_FILE"
    fi
}

# Sync JSON data to CSV
sync_json_to_csv() {
    log "INFO" "Syncing JSON data to CSV..."
    
    if [ ! -f "$JSON_FILE" ]; then
        log "ERROR" "JSON file not found: $JSON_FILE"
        return 1
    fi
    
    # Create CSV header
    echo "email,password,reward,status" > "$CSV_FILE"
    
    # Convert JSON to CSV
    jq -r '.[] | [.email, .password, .reward, .status] | @csv' "$JSON_FILE" >> "$CSV_FILE"
    
    log "SUCCESS" "JSON data synced to CSV file"
}

# Sync CSV data back to JSON
sync_csv_to_json() {
    log "INFO" "Syncing CSV data to JSON..."
    
    if [ ! -f "$CSV_FILE" ]; then
        log "ERROR" "CSV file not found: $CSV_FILE"
        return 1
    fi
    
    # Convert CSV to JSON (skip header)
    {
        echo "["
        tail -n +2 "$CSV_FILE" | while IFS=',' read -r email password reward status; do
            # Remove quotes from CSV fields
            email=$(echo "$email" | sed 's/^"//;s/"$//')
            password=$(echo "$password" | sed 's/^"//;s/"$//')
            reward=$(echo "$reward" | sed 's/^"//;s/"$//')
            status=$(echo "$status" | sed 's/^"//;s/"$//')
            
            cat << EOF
  {
    "email": "$email",
    "password": "$password",
    "reward": $reward,
    "status": "$status"
  },
EOF
        done | sed '$ s/,$//'
        echo "]"
    } > "$JSON_FILE"
    
    log "SUCCESS" "CSV data synced to JSON file"
}

# Read all accounts from JSON
read_accounts() {
    if [ ! -f "$JSON_FILE" ]; then
        log "ERROR" "JSON file not found: $JSON_FILE"
        return 1
    fi
    
    jq -r '.[] | "\(.email)|\(.password)|\(.reward)|\(.status)"' "$JSON_FILE"
}

# Get account by email
get_account() {
    local email="$1"
    
    if [ ! -f "$JSON_FILE" ]; then
        log "ERROR" "JSON file not found: $JSON_FILE"
        return 1
    fi
    
    jq -r --arg email "$email" '.[] | select(.email == $email) | "\(.email)|\(.password)|\(.reward)|\(.status)"' "$JSON_FILE"
}

# Update account status
update_account_status() {
    local email="$1"
    local new_status="$2"
    
    log "INFO" "Updating status for $email to $new_status"
    
    # Update JSON file
    jq --arg email "$email" --arg status "$new_status" \
       '(.[] | select(.email == $email) | .status) = $status' \
       "$JSON_FILE" > "${JSON_FILE}.tmp" && mv "${JSON_FILE}.tmp" "$JSON_FILE"
    
    # Sync to CSV
    sync_json_to_csv
    
    log "SUCCESS" "Updated status for $email to $new_status"
}

# Update account reward
update_account_reward() {
    local email="$1"
    local new_reward="$2"
    
    log "INFO" "Updating reward for $email to $new_reward"
    
    # Update JSON file
    jq --arg email "$email" --argjson reward "$new_reward" \
       '(.[] | select(.email == $email) | .reward) = $reward' \
       "$JSON_FILE" > "${JSON_FILE}.tmp" && mv "${JSON_FILE}.tmp" "$JSON_FILE"
    
    # Sync to CSV
    sync_json_to_csv
    
    log "SUCCESS" "Updated reward for $email to $new_reward"
}

# List all accounts
list_accounts() {
    log "INFO" "Listing all accounts:"
    echo
    printf "%-30s %-20s %-10s %-15s\n" "EMAIL" "PASSWORD" "REWARD" "STATUS"
    printf "%-30s %-20s %-10s %-15s\n" "-----" "--------" "------" "------"
    
    while IFS='|' read -r email password reward status; do
        # Mask password for display
        masked_password="${password:0:3}***${password: -2}"
        printf "%-30s %-20s %-10s %-15s\n" "$email" "$masked_password" "$reward" "$status"
    done < <(read_accounts)
    echo
}

# Process account function (placeholder for actual operations)
process_account() {
    local email="$1"
    local password="$2"
    local current_reward="$3"
    local current_status="$4"
    
    log "INFO" "Processing account: $email"
    
    # Update status to processing
    update_account_status "$email" "processing"
    
    # Simulate some processing time
    sleep 2
    
    # Example: Add some reward points (this is where you'd implement actual logic)
    local new_reward=$((current_reward + 10))
    update_account_reward "$email" "$new_reward"
    
    # Update status back to available
    update_account_status "$email" "available"
    
    log "SUCCESS" "Completed processing for $email"
}

# Process all available accounts
process_all_accounts() {
    log "INFO" "Starting to process all available accounts..."
    
    local processed_count=0
    
    while IFS='|' read -r email password reward status; do
        if [ "$status" = "available" ]; then
            process_account "$email" "$password" "$reward" "$status"
            ((processed_count++))
        else
            log "WARNING" "Skipping $email (status: $status)"
        fi
    done < <(read_accounts)
    
    log "SUCCESS" "Processed $processed_count accounts"
}

# Process accounts concurrently
process_accounts_concurrent() {
    log "INFO" "Starting concurrent processing of available accounts..."
    
    local pids=()
    local processed_count=0
    
    while IFS='|' read -r email password reward status; do
        if [ "$status" = "available" ]; then
            process_account "$email" "$password" "$reward" "$status" &
            pids+=($!)
            ((processed_count++))
        else
            log "WARNING" "Skipping $email (status: $status)"
        fi
    done < <(read_accounts)
    
    # Wait for all background processes to complete
    for pid in "${pids[@]}"; do
        wait "$pid"
    done
    
    log "SUCCESS" "Completed concurrent processing of $processed_count accounts"
}

# Show usage information
show_usage() {
    cat << EOF
Account Manager Script

Usage: $0 [COMMAND] [OPTIONS]

Commands:
    list                    List all accounts
    process                 Process all available accounts sequentially
    process-concurrent      Process all available accounts concurrently
    update-status EMAIL STATUS    Update account status
    update-reward EMAIL REWARD    Update account reward
    get EMAIL              Get specific account information
    sync-to-csv            Sync JSON data to CSV file
    sync-to-json           Sync CSV data to JSON file
    init                   Initialize CSV file

Examples:
    $0 list
    $0 process
    $0 update-status <EMAIL> processing
    $0 update-reward <EMAIL> 100
    $0 get <EMAIL>

EOF
}

# Main function
main() {
    # Check dependencies
    check_dependencies
    
    # Initialize CSV if it doesn't exist
    init_csv
    
    # Parse command line arguments
    case "${1:-}" in
        "list")
            list_accounts
            ;;
        "process")
            process_all_accounts
            ;;
        "process-concurrent")
            process_accounts_concurrent
            ;;
        "update-status")
            if [ $# -ne 3 ]; then
                log "ERROR" "Usage: $0 update-status EMAIL STATUS"
                exit 1
            fi
            update_account_status "$2" "$3"
            ;;
        "update-reward")
            if [ $# -ne 3 ]; then
                log "ERROR" "Usage: $0 update-reward EMAIL REWARD"
                exit 1
            fi
            update_account_reward "$2" "$3"
            ;;
        "get")
            if [ $# -ne 2 ]; then
                log "ERROR" "Usage: $0 get EMAIL"
                exit 1
            fi
            get_account "$2"
            ;;
        "sync-to-csv")
            sync_json_to_csv
            ;;
        "sync-to-json")
            sync_csv_to_json
            ;;
        "init")
            init_csv
            log "SUCCESS" "Initialized CSV file"
            ;;
        "help"|"-h"|"--help"|"")
            show_usage
            ;;
        *)
            log "ERROR" "Unknown command: $1"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
