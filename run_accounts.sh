#!/bin/bash

# Script to run Python main.py for each account in account.json
# Each account runs with 2 minutes delay between executions

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
JSON_FILE="${SCRIPT_DIR}/account.json"
LOG_FILE="${SCRIPT_DIR}/run_accounts.log"
DELAY_MINUTES=2

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
    
    case "$level" in
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message" >&2
            ;;
        "SUCCESS")
            echo -e "${GREEN}[SUCCESS]${NC} $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[WARNING]${NC} $message"
            ;;
        "INFO")
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
        *)
            echo "$message"
            ;;
    esac
}

# Check if required files exist
check_requirements() {
    if [ ! -f "$JSON_FILE" ]; then
        log "ERROR" "JSON file not found: $JSON_FILE"
        exit 1
    fi
    
    if [ ! -f "src/main.py" ]; then
        log "ERROR" "Python main.py not found in src/ directory"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log "ERROR" "jq is required but not installed"
        log "INFO" "Install jq: sudo apt-get install jq (Ubuntu/Debian) or brew install jq (macOS)"
        exit 1
    fi
    
    if ! command -v python3 &> /dev/null; then
        log "ERROR" "python3 is required but not installed"
        exit 1
    fi
}

# Update account status in JSON file
update_account_status() {
    local email="$1"
    local new_status="$2"
    
    log "INFO" "Updating status for $email to $new_status"
    
    # Create backup
    cp "$JSON_FILE" "${JSON_FILE}.backup"
    
    # Update JSON file
    jq --arg email "$email" --arg status "$new_status" \
       '(.[] | select(.email == $email) | .status) = $status' \
       "$JSON_FILE" > "${JSON_FILE}.tmp" && mv "${JSON_FILE}.tmp" "$JSON_FILE"
    
    log "SUCCESS" "Updated status for $email to $new_status"
}

# Update account reward in JSON file
update_account_reward() {
    local email="$1"
    local new_reward="$2"
    
    log "INFO" "Updating reward for $email to $new_reward"
    
    # Update JSON file
    jq --arg email "$email" --argjson reward "$new_reward" \
       '(.[] | select(.email == $email) | .reward) = $reward' \
       "$JSON_FILE" > "${JSON_FILE}.tmp" && mv "${JSON_FILE}.tmp" "$JSON_FILE"
    
    log "SUCCESS" "Updated reward for $email to $new_reward"
}

# Run Python script for a single account
run_account() {
    local email="$1"
    local password="$2"
    local current_reward="$3"
    local current_status="$4"
    
    log "INFO" "Starting processing for account: $email"
    
    # Skip if account is not available
    if [ "$current_status" != "available" ]; then
        log "WARNING" "Skipping $email (status: $current_status)"
        return 0
    fi
    
    # Update status to processing
    update_account_status "$email" "processing"
    
    # Run the Python script
    log "INFO" "Running: python3 src/main.py --email $email --password [HIDDEN] --headless"
    
    local start_time=$(date +%s)
    local exit_code=0
    
    # Execute the Python command and capture output
    if python3 src/main.py --email "$email" --password "$password" --headless 2>&1 | tee -a "$LOG_FILE"; then
        log "SUCCESS" "Python script completed successfully for $email"
        
        # Update status back to available
        update_account_status "$email" "available"
        
        # Optionally update reward (you can modify this logic as needed)
        local new_reward=$((current_reward + 10))
        update_account_reward "$email" "$new_reward"
        
    else
        exit_code=$?
        log "ERROR" "Python script failed for $email (exit code: $exit_code)"
        
        # Update status to error
        update_account_status "$email" "error"
    fi
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    log "INFO" "Processing completed for $email in ${duration}s"
    
    return $exit_code
}

# Process all available accounts
process_all_accounts() {
    log "INFO" "Starting to process all available accounts..."
    log "INFO" "Delay between accounts: ${DELAY_MINUTES} minutes"
    
    local total_accounts=0
    local processed_accounts=0
    local failed_accounts=0
    local skipped_accounts=0
    
    # Read accounts from JSON file
    local account_data
    account_data=$(jq -r '.[] | "\(.email)|\(.password)|\(.reward)|\(.status)"' "$JSON_FILE")

    if [ -z "$account_data" ]; then
        log "ERROR" "No account data found or failed to read JSON file"
        return 1
    fi

    log "INFO" "Found account data:"
    log "INFO" "$account_data"

    while IFS='|' read -r email password reward status; do
        ((total_accounts++))

        log "INFO" "Account $total_accounts: $email (status: $status, reward: $reward)"

        if [ "$status" = "available" ]; then
            if run_account "$email" "$password" "$reward" "$status"; then
                ((processed_accounts++))
            else
                ((failed_accounts++))
            fi

            # Add delay between accounts (except for the last one)
            local total_count
            total_count=$(jq length "$JSON_FILE")
            if [ $total_accounts -lt $total_count ]; then
                log "INFO" "Waiting ${DELAY_MINUTES} minutes before next account..."
                sleep $((DELAY_MINUTES * 60))
            fi
        else
            ((skipped_accounts++))
            log "WARNING" "Skipped $email (status: $status)"
        fi

    done <<< "$account_data"
    
    # Summary
    log "INFO" "=== Processing Summary ==="
    log "INFO" "Total accounts: $total_accounts"
    log "INFO" "Processed successfully: $processed_accounts"
    log "INFO" "Failed: $failed_accounts"
    log "INFO" "Skipped: $skipped_accounts"
    
    if [ $failed_accounts -eq 0 ]; then
        log "SUCCESS" "All available accounts processed successfully!"
    else
        log "WARNING" "$failed_accounts accounts failed processing"
    fi
}

# Process accounts concurrently (all at once)
process_concurrent() {
    log "INFO" "Starting concurrent processing of all available accounts..."
    
    local pids=()
    local total_accounts=0
    
    # Start all available accounts in background
    local account_data
    account_data=$(jq -r '.[] | "\(.email)|\(.password)|\(.reward)|\(.status)"' "$JSON_FILE")

    if [ -z "$account_data" ]; then
        log "ERROR" "No account data found or failed to read JSON file"
        return 1
    fi

    while IFS='|' read -r email password reward status; do
        ((total_accounts++))

        if [ "$status" = "available" ]; then
            log "INFO" "Starting background process for: $email"
            run_account "$email" "$password" "$reward" "$status" &
            pids+=($!)
        else
            log "WARNING" "Skipping $email (status: $status)"
        fi

    done <<< "$account_data"
    
    # Wait for all background processes to complete
    log "INFO" "Waiting for all processes to complete..."
    local failed_count=0
    
    for pid in "${pids[@]}"; do
        if ! wait "$pid"; then
            ((failed_count++))
        fi
    done
    
    if [ $failed_count -eq 0 ]; then
        log "SUCCESS" "All concurrent processes completed successfully!"
    else
        log "WARNING" "$failed_count processes failed"
    fi
}

# List all accounts with their current status
list_accounts() {
    log "INFO" "Current account status:"
    echo
    printf "%-35s %-10s %-15s\n" "EMAIL" "REWARD" "STATUS"
    printf "%-35s %-10s %-15s\n" "-----" "------" "------"
    
    local account_data
    account_data=$(jq -r '.[] | "\(.email)|\(.password)|\(.reward)|\(.status)"' "$JSON_FILE")

    while IFS='|' read -r email password reward status; do
        printf "%-35s %-10s %-15s\n" "$email" "$reward" "$status"
    done <<< "$account_data"
    echo
}

# Reset all account statuses to available
reset_statuses() {
    log "INFO" "Resetting all account statuses to 'available'..."
    
    # Create backup
    cp "$JSON_FILE" "${JSON_FILE}.backup"
    
    # Reset all statuses
    jq 'map(.status = "available")' "$JSON_FILE" > "${JSON_FILE}.tmp" && mv "${JSON_FILE}.tmp" "$JSON_FILE"
    
    log "SUCCESS" "All account statuses reset to 'available'"
}

# Show usage information
show_usage() {
    cat << EOF
Account Runner Script

Usage: $0 [COMMAND] [OPTIONS]

Commands:
    run                     Process all available accounts sequentially (2min delay)
    run-concurrent          Process all available accounts concurrently
    list                    List all accounts with their status
    reset                   Reset all account statuses to 'available'

Options:
    --delay MINUTES         Set delay between accounts (default: 2 minutes)

Examples:
    $0 run                  # Run all available accounts with 2min delay
    $0 run --delay 5        # Run all available accounts with 5min delay
    $0 run-concurrent       # Run all available accounts at the same time
    $0 list                 # Show current account status
    $0 reset                # Reset all statuses to available

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --delay)
                DELAY_MINUTES="$2"
                shift 2
                ;;
            *)
                break
                ;;
        esac
    done
}

# Main function
main() {
    # Parse arguments first
    parse_args "$@"
    
    # Remove parsed arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --delay)
                shift 2
                ;;
            *)
                break
                ;;
        esac
    done
    
    # Check requirements
    check_requirements
    
    # Handle commands
    case "${1:-}" in
        "run")
            process_all_accounts
            ;;
        "run-concurrent")
            process_concurrent
            ;;
        "list")
            list_accounts
            ;;
        "reset")
            reset_statuses
            ;;
        "help"|"-h"|"--help"|"")
            show_usage
            ;;
        *)
            log "ERROR" "Unknown command: $1"
            show_usage
            exit 1
            ;;
    esac
}

# Initialize log file
echo "=== Account Runner Started at $(date) ===" >> "$LOG_FILE"

# Run main function with all arguments
main "$@"
