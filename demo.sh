#!/bin/bash

# Demo script showing how to use the account manager system

set -euo pipefail

echo "=== Account Manager System Demo ==="
echo

echo "1. Listing all accounts:"
./account_manager.sh list
echo

echo "2. Showing account statistics:"
./account_utils.sh stats
echo

echo "3. Updating an account status to 'available':"
./account_manager.sh update-status <EMAIL> available
echo

echo "4. Updating account reward:"
./account_manager.sh update-reward <EMAIL> 25
echo

echo "5. Getting specific account info:"
./account_manager.<NAME_EMAIL>
echo

echo "6. Processing available accounts (demo):"
./account_manager.sh process
echo

echo "7. Final account list:"
./account_manager.sh list
echo

echo "8. Final statistics:"
./account_utils.sh stats

echo "=== Demo completed ==="
